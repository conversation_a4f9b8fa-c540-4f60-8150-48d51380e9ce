import sys
import io
import traceback
import threading
import time
import ast
import resource
import signal
import multiprocessing
import os
from contextlib import contextmanager

# 在 Python 3.9+ 中，ast.unparse 是标准库的一部分。
# 如果在旧版本运行，可能需要安装 astunparse 库。

class SecurityError(Exception):
    """用于安全相关错误的自定义异常。"""
    pass

class MemoryError(Exception):
    """内存使用超限异常"""
    pass

class ExecutionError(Exception):
    """代码执行错误的基类"""
    def __init__(self, message, error_type=None, line_number=None):
        super().__init__(message)
        self.error_type = error_type
        self.line_number = line_number

class LoopProtector(ast.NodeTransformer):
    """
    一个 AST 转换器，用于在循环中插入保护性检查。
    它会在每个 for 和 while 循环体的开头添加一个对 `__check_loop()` 函数的调用。
    """
    def _create_check_call(self):
        """创建一个表示 `__check_loop()` 调用的 AST 节点。"""
        return ast.Expr(value=ast.Call(
            func=ast.Name(id='__check_loop', ctx=ast.Load()),
            args=[],
            keywords=[]
        ))

    def visit_For(self, node):
        """访问 for 循环节点。"""
        # 在循环体的最前面插入检查函数
        node.body.insert(0, self._create_check_call())
        # 确保递归访问子节点
        self.generic_visit(node)
        return node

    def visit_While(self, node):
        """访问 while 循环节点。"""
        # 在循环体的最前面插入检查函数
        node.body.insert(0, self._create_check_call())
        # 确保递归访问子节点
        self.generic_visit(node)
        return node

class SecureSandbox:
    """
    一个增强的安全沙盒，用于执行不受信任的Python代码字符串。

    安全特性：
    1. 严格限制的内建函数白名单
    2. 禁止导入任何模块
    3. 禁止文件I/O和网络访问
    4. 执行超时保护
    5. 递归深度限制
    6. AST层面的代码分析和保护
    7. 禁止访问私有属性和危险方法
    """

    def __init__(self, timeout=5, max_recursion_depth=100, max_output_length=10000, max_loop_iterations=100000, max_memory_mb=50):
        """
        初始化沙盒配置
        
        Args:
            timeout: 代码执行超时时间（秒）
            max_recursion_depth: 最大递归深度
            max_output_length: 最大输出长度
            max_loop_iterations: 最大循环迭代次数
            max_memory_mb: 最大内存使用量（MB）
        """
        self.timeout = timeout
        self.max_recursion_depth = max_recursion_depth
        self.max_output_length = max_output_length
        self.max_loop_iterations = max_loop_iterations
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        
        # 安全的内建函数和类型白名单
        safe_builtins_list = [
            'abs', 'all', 'any', 'ascii', 'bin', 'bool', 'bytearray', 'bytes',
            'callable', 'chr', 'complex', 'dict', 'divmod', 'enumerate',
            'filter', 'float', 'format', 'frozenset', 'hash', 'hex', 'id',
            'int', 'isinstance', 'issubclass', 'iter', 'len', 'list', 'map',
            'max', 'min', 'next', 'object', 'oct', 'ord', 'pow', 'range', 
            'repr', 'reversed', 'round', 'set', 'slice', 'sorted',
            'str', 'sum', 'super', 'tuple', 'type', 'zip', 
            'None', 'True', 'False'
        ]
        
        # 安全的异常类型白名单
        safe_exceptions = [
            'Exception', 'ValueError', 'TypeError', 'IndexError', 'KeyError',
            'AttributeError', 'NameError', 'RuntimeError', 'SyntaxError'
        ]
        
        # 安全的标准库模块白名单
        self.safe_modules = {
            'math': ['sin', 'cos', 'tan', 'sqrt', 'log', 'log10', 'exp', 'pi', 'e',
                    'ceil', 'floor', 'fabs', 'factorial', 'gcd', 'pow', 'degrees', 'radians'],
            'random': ['random', 'randint', 'choice', 'shuffle', 'sample', 'seed'],
            'datetime': ['datetime', 'date', 'time', 'timedelta'],
            'json': ['loads', 'dumps'],
            're': ['match', 'search', 'findall', 'sub', 'split', 'compile'],
            'collections': ['Counter', 'defaultdict', 'deque', 'namedtuple'],
            'itertools': ['count', 'cycle', 'repeat', 'chain', 'combinations', 'permutations'],
            'functools': ['reduce', 'partial'],
            'operator': ['add', 'sub', 'mul', 'truediv', 'mod', 'pow', 'and_', 'or_', 'xor'],
            'string': ['ascii_letters', 'ascii_lowercase', 'ascii_uppercase', 'digits', 'punctuation'],
            'pandas': ['read_excel', 'read_csv', 'DataFrame', 'Series', 'to_datetime', 'concat', 'merge'],
            'openpyxl': ['load_workbook', 'Workbook'],
            'xlrd': ['open_workbook'],
            'csv': ['reader', 'writer', 'DictReader', 'DictWriter']
        }
        
        # 可视化库白名单 - 使用更宽松的策略
        self.visualization_modules = {
            'matplotlib', 'matplotlib.pyplot', 'numpy', 'pandas'
        }
        
        # 保存原始的导入函数
        self._original_import = __import__
        
        restricted_builtins = {}
        # 处理 __builtins__ 可能是字典或模块的情况
        builtins_dict = __builtins__ if isinstance(__builtins__, dict) else __builtins__.__dict__
        
        for name in safe_builtins_list + safe_exceptions:
            if name in builtins_dict:
                restricted_builtins[name] = builtins_dict[name]
        
        # 添加受限的print函数
        restricted_builtins['print'] = self._safe_print

        # 添加安全的文件读取函数
        restricted_builtins['safe_read_file'] = self._safe_read_file

        # 对于普通模块使用安全导入，但为了避免matplotlib的递归问题，
        # 我们需要一个更简单的解决方案
        restricted_builtins['__import__'] = self._safe_import
        
        # 构建安全的全局环境
        self.safe_globals = {
            "__builtins__": restricted_builtins,
            "__name__": "__sandbox__",
        }

    def _safe_print(self, *args, **kwargs):
        """
        安全的print函数，它会写入到被重定向的 sys.stdout。
        输出长度截断由 execute 方法在最后统一处理。
        """
        # print() 默认写入 sys.stdout, 而我们已经重定向了它，所以直接调用即可。
        print(*args, **kwargs)

    def _safe_read_file(self, filepath, encoding='utf-8', max_size_mb=10):
        """
        安全的文件读取函数，只允许读取当前工作目录下的文件

        Args:
            filepath: 文件路径（相对路径）
            encoding: 文件编码，默认utf-8
            max_size_mb: 最大文件大小限制（MB）

        Returns:
            文件内容字符串
        """
        import os

        # 获取当前工作目录
        current_dir = os.getcwd()

        # 将相对路径转换为绝对路径
        abs_filepath = os.path.abspath(os.path.join(current_dir, filepath))

        # 安全检查：确保文件在当前工作目录下
        if not abs_filepath.startswith(current_dir):
            raise SecurityError(f"禁止访问工作目录外的文件: {filepath}")

        # 检查文件是否存在
        if not os.path.exists(abs_filepath):
            raise FileNotFoundError(f"文件不存在: {filepath}")

        # 检查是否是文件（不是目录）
        if not os.path.isfile(abs_filepath):
            raise ValueError(f"路径不是文件: {filepath}")

        # 检查文件大小
        file_size = os.path.getsize(abs_filepath)
        max_size_bytes = max_size_mb * 1024 * 1024
        if file_size > max_size_bytes:
            raise ValueError(f"文件过大: {file_size} bytes > {max_size_bytes} bytes")

        # 读取文件
        try:
            with open(abs_filepath, 'r', encoding=encoding) as f:
                content = f.read()
            return content
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，尝试其他编码
            try:
                with open(abs_filepath, 'r', encoding='gbk') as f:
                    content = f.read()
                return content
            except UnicodeDecodeError:
                # 如果文本解码都失败，以二进制模式读取
                with open(abs_filepath, 'rb') as f:
                    binary_content = f.read()
                return f"[二进制文件，大小: {len(binary_content)} bytes]"
        except Exception as e:
            raise RuntimeError(f"读取文件失败: {str(e)}")

    def _safe_import(self, name, globals=None, locals=None, fromlist=(), level=0):
        """
        安全的导入函数，只允许导入白名单中的模块和函数
        """
        # 处理子模块导入（如 matplotlib.pyplot）
        main_module = name.split('.')[0]
        
        # 对于可视化库，使用原始的内置导入函数，避免递归
        if any(name.startswith(viz_mod) for viz_mod in self.visualization_modules):
            # 使用原始保存的导入函数，绕过当前的安全检查
            import builtins
            original_import = getattr(builtins, '__import__')
            return original_import(name, globals, locals, fromlist, level)
        
        # 检查是否是允许的模块
        if name not in self.safe_modules and main_module not in self.safe_modules:
            raise SecurityError(f"禁止导入模块: {name}")
        
        try:
            # 对于其他模块，使用原始导入并限制属性
            import builtins
            original_import = getattr(builtins, '__import__')
            real_module = original_import(name, globals, locals, fromlist, level)
            
            # 获取允许的属性列表
            if name in self.safe_modules:
                allowed_attrs = self.safe_modules[name]
            else:
                allowed_attrs = self.safe_modules[main_module]
            
            # 创建受限的模块代理
            if fromlist:
                # from module import xxx 的情况
                limited_attrs = {}
                for attr_name in fromlist:
                    if attr_name in allowed_attrs and hasattr(real_module, attr_name):
                        limited_attrs[attr_name] = getattr(real_module, attr_name)
                    else:
                        raise SecurityError(f"禁止从模块 {name} 导入: {attr_name}")
                
                # 创建一个模拟模块对象
                class LimitedModule:
                    pass
                
                limited_module = LimitedModule()
                for attr_name, attr_value in limited_attrs.items():
                    setattr(limited_module, attr_name, attr_value)
                
                return limited_module
            else:
                # import module 的情况
                class LimitedModule:
                    def __init__(self, real_mod, allowed):
                        self._real_module = real_mod
                        self._allowed_attrs = allowed
                        
                        # 预加载允许的属性
                        for attr_name in allowed:
                            if hasattr(real_mod, attr_name):
                                setattr(self, attr_name, getattr(real_mod, attr_name))
                    
                    def __getattr__(self, name):
                        if name in self._allowed_attrs and hasattr(self._real_module, name):
                            return getattr(self._real_module, name)
                        raise SecurityError(f"禁止访问模块属性: {name}")
                
                return LimitedModule(real_module, allowed_attrs)
                
        except ImportError:
            # 如果模块不存在，返回原始错误
            raise
        except Exception as e:
            raise SecurityError(f"模块导入失败: {str(e)}")

    @contextmanager
    def _execution_limits(self):
        """设置执行限制的上下文管理器。"""
        original_recursion_limit = sys.getrecursionlimit()
        original_alarm = signal.alarm(0)  # 清除之前的alarm
        
        try:
            # 设置递归深度限制
            sys.setrecursionlimit(self.max_recursion_depth)
            
            # 设置内存限制（仅在Unix-like系统上有效）
            if hasattr(resource, 'RLIMIT_AS'):
                try:
                    resource.setrlimit(resource.RLIMIT_AS, (self.max_memory_bytes, self.max_memory_bytes))
                except (ValueError, OSError):
                    # 在某些系统上可能失败，继续执行但记录警告
                    pass
            
            # 设置真正的超时机制
            def timeout_handler(signum, frame):
                raise TimeoutError(f"代码执行超时（>{self.timeout}秒）")
            
            if hasattr(signal, 'SIGALRM'):
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(int(self.timeout))
            
            yield
            
        finally:
            # 恢复设置
            sys.setrecursionlimit(original_recursion_limit)
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)  # 取消alarm
            
            # 重置内存限制
            if hasattr(resource, 'RLIMIT_AS'):
                try:
                    resource.setrlimit(resource.RLIMIT_AS, (resource.RLIM_INFINITY, resource.RLIM_INFINITY))
                except (ValueError, OSError):
                    pass

    def _validate_ast(self, node):
        """通过AST分析验证代码安全性。"""
        class SecurityVisitor(ast.NodeVisitor):
            def __init__(self, parent_sandbox):
                self.violations = []
                self.parent_sandbox = parent_sandbox
                
            def visit_Import(self, node):
                # 现在允许导入白名单中的模块
                for alias in node.names:
                    module_name = alias.name
                    main_module = module_name.split('.')[0]
                    
                    # 对于可视化模块，完全跳过检查
                    if any(module_name.startswith(viz_mod) for viz_mod in self.parent_sandbox.visualization_modules):
                        continue
                    
                    # 检查是否允许导入
                    if (module_name not in self.parent_sandbox.safe_modules and 
                        main_module not in self.parent_sandbox.safe_modules):
                        self.violations.append(f"禁止导入模块: {module_name}")
                
            def visit_ImportFrom(self, node):
                # 现在允许从白名单模块导入
                module_name = node.module
                if not module_name:
                    return
                main_module = module_name.split('.')[0]
                
                # 对于可视化模块，完全跳过检查
                if any(module_name.startswith(viz_mod) for viz_mod in self.parent_sandbox.visualization_modules):
                    return
                
                # 检查是否允许导入
                if (module_name not in self.parent_sandbox.safe_modules and 
                    main_module not in self.parent_sandbox.safe_modules):
                    self.violations.append(f"禁止从模块导入: {module_name}")
                else:
                    # 检查导入的具体项目是否在白名单中
                    if module_name in self.parent_sandbox.safe_modules:
                        allowed_attrs = self.parent_sandbox.safe_modules[module_name]
                    else:
                        allowed_attrs = self.parent_sandbox.safe_modules[main_module]
                    
                    for alias in node.names:
                        if alias.name not in allowed_attrs:
                            self.violations.append(f"禁止从模块 {module_name} 导入: {alias.name}")
                
            def visit_Call(self, node):
                # 检查对危险函数的调用
                if isinstance(node.func, ast.Name):
                    func_name = node.func.id
                    dangerous_funcs = [
                        'eval', 'exec', 'compile', 'open', 'file', '__import__',
                        'getattr', 'setattr', 'delattr', 'globals', 'locals', 'vars'
                    ]
                    if func_name in dangerous_funcs:
                        self.violations.append(f"禁止调用危险函数: {func_name}")
                
                self.generic_visit(node)
                
            def visit_Attribute(self, node):
                # 检查对敏感属性的访问
                if isinstance(node.attr, str) and node.attr.startswith('_'):
                    sensitive_attrs = ['__dict__', '__class__', '__subclasses__', '__globals__', '__code__', '__mro__', '__bases__']
                    if node.attr in sensitive_attrs:
                        self.violations.append(f"禁止访问敏感属性: {node.attr}")
                self.generic_visit(node)
        
        visitor = SecurityVisitor(self)
        visitor.visit(node)
        return visitor.violations

    def _instrument_code(self, code_string: str, enable_loop_protection: bool):
        """
        解析、验证和注入代码以确保安全。
        这是一个核心的重构方法，使用AST处理代码。
        """
        # 1. 将代码解析为AST
        tree = ast.parse(code_string, '<sandbox>')

        # 2. 对AST进行安全验证
        violations = self._validate_ast(tree)
        if violations:
            raise SecurityError("代码安全检查失败: " + "; ".join(violations))

        # 3. (可选) 注入循环保护
        if enable_loop_protection:
            transformer = LoopProtector()
            tree = transformer.visit(tree)
            ast.fix_missing_locations(tree) # 修复新添加节点的位置信息

            # 定义需要注入的保护函数
            protection_code_template = f"""
# 循环保护助手
__loop_counter = 0
def __check_loop():
    global __loop_counter
    __loop_counter += 1
    if __loop_counter > {self.max_loop_iterations}:
        raise RuntimeError(f"循环迭代次数超过限制: {self.max_loop_iterations}")

"""
            # 将修改后的AST转换回代码字符串，并前置保护函数
            final_code = protection_code_template + ast.unparse(tree)
            return final_code
        else:
            # 如果不启用循环保护，则返回原始代码
            return code_string

    def execute(self, code_to_run: str, enable_loop_protection=True):
        """
        在沙盒环境中执行代码
        
        Args:
            code_to_run: 要执行的Python代码字符串
            enable_loop_protection: 是否启用循环保护
            
        Returns:
            字典包含 'stdout', 'stderr', 'error', 'execution_time'
        """
        start_time = time.time()
        output_capture = io.StringIO()
        error_capture = io.StringIO()
        
        original_stdout = sys.stdout
        original_stderr = sys.stderr
        
        result = {
            'stdout': '',
            'stderr': '',
            'error': None,
            'execution_time': 0
        }
        
        try:
            # 重定向输出流
            sys.stdout = output_capture
            sys.stderr = error_capture

            # 首先进行AST级别的安全检查和代码注入
            instrumented_code = self._instrument_code(code_to_run, enable_loop_protection)
            
            # 在限制环境中执行代码
            with self._execution_limits():
                # 编译代码
                compiled_code = compile(instrumented_code, '<sandbox>', 'exec')
                
                # 创建执行环境
                execution_globals = self.safe_globals.copy()
                
                # 检查代码是否包含matplotlib导入，如果是，临时禁用安全导入
                if any(viz_mod in code_to_run for viz_mod in self.visualization_modules):
                    # 临时恢复原始的__import__函数来避免递归问题
                    import builtins
                    original_builtin_import = builtins.__import__
                    execution_globals["__builtins__"]["__import__"] = original_builtin_import
                
                # 执行代码
                exec(compiled_code, execution_globals)
                
        except SecurityError as e:
            result['error'] = f"安全错误: {str(e)}"
            result['error_type'] = "security_error"
        except TimeoutError as e:
            result['error'] = f"执行超时: {str(e)}"
            result['error_type'] = "timeout_error"
        except MemoryError as e:
            result['error'] = f"内存超限: {str(e)}"
            result['error_type'] = "memory_error"
        except SyntaxError as e:
            result['error'] = f"语法错误: {e.msg} (在第 {e.lineno} 行)"
            result['error_type'] = "syntax_error"
            result['line_number'] = e.lineno
        except RecursionError as e:
            result['error'] = f"递归深度超限: 超过最大递归深度 {self.max_recursion_depth}"
            result['error_type'] = "recursion_error"
        except (KeyboardInterrupt, SystemExit):
            result['error'] = "代码尝试中断执行或退出系统"
            result['error_type'] = "interrupt_error"
        except Exception as e:
            # 增强的异常处理，提供更多信息
            error_info = traceback.format_exc()
            exception_name = type(e).__name__
            result['error'] = f"{exception_name}: {str(e)}"
            result['error_type'] = "runtime_error"
            result['traceback'] = error_info
        finally:
            # 恢复输出流
            sys.stdout = original_stdout
            sys.stderr = original_stderr
            
            # 获取捕获的输出并限制长度
            stdout_content = output_capture.getvalue()
            if len(stdout_content) > self.max_output_length:
                stdout_content = stdout_content[:self.max_output_length] + '\n...[输出被截断]'
            
            result['stdout'] = stdout_content
            result['stderr'] = error_capture.getvalue()
            result['execution_time'] = time.time() - start_time
            
            # 清理资源
            output_capture.close()
            error_capture.close()
        
        return result


class PythonCodeExecutor:
    """
    标准化的Python代码执行工具类，符合LLM Tool规范
    """
    
    def __init__(self, config=None):
        """
        初始化执行器
        
        Args:
            config: 配置字典，包含沙盒参数
        """
        config = config or {}
        self.sandbox = SecureSandbox(
            timeout=config.get('timeout', 5),
            max_recursion_depth=config.get('max_recursion_depth', 100),
            max_output_length=config.get('max_output_length', 10000),
            max_loop_iterations=config.get('max_loop_iterations', 100000),
            max_memory_mb=config.get('max_memory_mb', 50)
        )
    
    def execute_code(self, code: str, mode: str = "strict") -> dict:
        """
        执行Python代码的标准接口
        
        Args:
            code: 要执行的Python代码字符串
            mode: 执行模式 ("strict", "normal", "permissive")
            
        Returns:
            标准化的执行结果字典:
            {
                "success": bool,           # 执行是否成功
                "output": str,             # 标准输出
                "error": str or None,      # 错误信息
                "execution_time": float,   # 执行时间
                "memory_used": int,        # 使用的内存（字节）
                "warnings": list           # 警告信息
            }
        """
        # 对于permissive模式且包含可视化库的代码，使用不同的执行策略
        if mode == "permissive" and any(viz_mod in code for viz_mod in self.sandbox.visualization_modules):
            return self._execute_permissive_visualization(code)
        
        # 根据模式调整保护级别
        enable_loop_protection = mode in ["strict", "normal"]
        
        # 执行代码
        result = self.sandbox.execute(code, enable_loop_protection=enable_loop_protection)
        
        # 标准化返回格式
        standardized_result = {
            "success": result['error'] is None,
            "output": result['stdout'],
            "error": result['error'],
            "execution_time": result['execution_time'],
            "memory_used": 0,  # TODO: 实现内存使用统计
            "warnings": []
        }
        
        # 执行结果验证
        warnings = self._validate_execution_result(standardized_result)
        standardized_result["warnings"] = warnings
        
        return standardized_result
    
    def _execute_permissive_visualization(self, code: str) -> dict:
        """
        为包含可视化库的代码使用宽松的执行模式
        """
        import io
        import sys
        import time
        import signal
        import resource
        from contextlib import contextmanager
        
        start_time = time.time()
        output_capture = io.StringIO()
        error_capture = io.StringIO()
        
        original_stdout = sys.stdout
        original_stderr = sys.stderr
        
        result = {
            'stdout': '',
            'stderr': '',
            'error': None,
            'execution_time': 0
        }
        
        @contextmanager
        def basic_limits():
            """设置基本的执行限制"""
            original_recursion_limit = sys.getrecursionlimit()
            
            try:
                # 设置更高的递归深度限制，因为matplotlib需要更深的调用栈
                sys.setrecursionlimit(1000)  # 增加到1000而不是100
                
                # 设置内存限制（仅在Unix-like系统上有效）
                if hasattr(resource, 'RLIMIT_AS'):
                    try:
                        resource.setrlimit(resource.RLIMIT_AS, (self.sandbox.max_memory_bytes, self.sandbox.max_memory_bytes))
                    except (ValueError, OSError):
                        pass
                
                # 设置超时
                def timeout_handler(signum, frame):
                    raise TimeoutError(f"代码执行超时（>{self.sandbox.timeout}秒）")
                
                if hasattr(signal, 'SIGALRM'):
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(int(self.sandbox.timeout))
                
                yield
                
            finally:
                # 恢复设置
                sys.setrecursionlimit(original_recursion_limit)
                if hasattr(signal, 'SIGALRM'):
                    signal.alarm(0)
                
                # 重置内存限制
                if hasattr(resource, 'RLIMIT_AS'):
                    try:
                        resource.setrlimit(resource.RLIMIT_AS, (resource.RLIM_INFINITY, resource.RLIM_INFINITY))
                    except (ValueError, OSError):
                        pass
        
        try:
            # 重定向输出流
            sys.stdout = output_capture
            sys.stderr = error_capture
            
            # 使用基本限制执行代码，但不使用自定义的import函数
            with basic_limits():
                # 创建一个最小的执行环境，使用原始的__builtins__但添加我们的安全函数
                if isinstance(__builtins__, dict):
                    builtins_dict = __builtins__.copy()
                else:
                    builtins_dict = __builtins__.__dict__.copy()

                # 添加我们的安全函数
                builtins_dict['safe_read_file'] = self.sandbox._safe_read_file

                execution_globals = {
                    "__builtins__": builtins_dict,
                    "__name__": "__sandbox__",
                }
                
                # 如果代码包含matplotlib，预先设置中文字体支持
                if "matplotlib" in code:
                    # 添加中文字体配置代码
                    font_setup_code = """
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import base64
import io
from datetime import datetime

# 配置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Hiragino Sans GB', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False  # 解决坐标轴负号显示问题

# 如果系统有可用的中文字体，设置为默认
try:
    # 查找系统中可用的中文字体
    chinese_fonts = []
    for font in fm.fontManager.ttflist:
        if any(name in font.name for name in ['SimHei', 'Microsoft YaHei', 'Hiragino Sans GB', 'PingFang', 'STHeiti']):
            chinese_fonts.append(font.name)
            break
    
    if chinese_fonts:
        plt.rcParams['font.family'] = chinese_fonts[0]
        print(f"✅ 已设置中文字体: {chinese_fonts[0]}")
    else:
        print("⚠️  未找到中文字体，使用默认字体（可能显示方框）")
except Exception as e:
    print(f"⚠️  字体设置警告: {e}")

# 创建图片保存目录
_chart_dir = "/tmp/matplotlib_charts"
os.makedirs(_chart_dir, exist_ok=True)

# 图片保存函数
def save_current_figure(title="chart", format="png", dpi=150):
    \"\"\"保存当前matplotlib图形并返回文件路径和base64编码\"\"\"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
    filename = f"{title}_{timestamp}.{format}"
    filepath = os.path.join(_chart_dir, filename)
    
    # 保存图片文件
    plt.savefig(filepath, format=format, dpi=dpi, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    # 生成base64编码（用于在线预览）
    buffer = io.BytesIO()
    plt.savefig(buffer, format=format, dpi=dpi, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    buffer.seek(0)
    img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    buffer.close()
    
    print(f"📊 图片已保存: {filepath}")
    print(f"📊 图片大小: {len(img_base64)} bytes (base64)")
    
    return {
        'filepath': filepath,
        'filename': filename,
        'base64': img_base64,
        'format': format,
        'dpi': dpi
    }

# 自动保存函数（在plt.show()或plt.close()时调用）
_original_show = plt.show
_original_close = plt.close
_chart_counter = 0

def _auto_save_show(*args, **kwargs):
    global _chart_counter
    _chart_counter += 1
    save_info = save_current_figure(f"chart_{_chart_counter}")
    return _original_show(*args, **kwargs)

def _auto_save_close(*args, **kwargs):
    global _chart_counter
    _chart_counter += 1
    save_info = save_current_figure(f"chart_{_chart_counter}")
    return _original_close(*args, **kwargs)

# 替换plt.show和plt.close为自动保存版本
plt.show = _auto_save_show
plt.close = _auto_save_close

print("📊 图片自动保存功能已启用")
"""
                    # 将字体配置代码添加到用户代码前面
                    code = font_setup_code + "\n\n# 用户代码开始\n" + code
                
                # 编译并执行代码
                compiled_code = compile(code, '<permissive_sandbox>', 'exec')
                exec(compiled_code, execution_globals)
                
        except TimeoutError as e:
            result['error'] = f"执行超时: {str(e)}"
        except MemoryError as e:
            result['error'] = f"内存超限: {str(e)}"
        except SyntaxError as e:
            result['error'] = f"语法错误: {e.msg} (在第 {e.lineno} 行)"
        except RecursionError as e:
            result['error'] = f"递归深度超限: 超过最大递归深度 {self.sandbox.max_recursion_depth}"
        except (KeyboardInterrupt, SystemExit):
            result['error'] = "代码尝试中断执行或退出系统"
        except Exception as e:
            import traceback
            error_info = traceback.format_exc()
            exception_name = type(e).__name__
            result['error'] = f"{exception_name}: {str(e)}"
        finally:
            # 恢复输出流
            sys.stdout = original_stdout
            sys.stderr = original_stderr
            
            # 获取捕获的输出并限制长度
            stdout_content = output_capture.getvalue()
            max_length = self.sandbox.max_output_length
            if len(stdout_content) > max_length:
                stdout_content = stdout_content[:max_length] + '\n...[输出被截断]'
            
            result['stdout'] = stdout_content
            result['stderr'] = error_capture.getvalue()
            result['execution_time'] = time.time() - start_time
            
            # 清理资源
            output_capture.close()
            error_capture.close()
        
        # 标准化返回格式
        standardized_result = {
            "success": result['error'] is None,
            "output": result['stdout'],
            "error": result['error'],
            "execution_time": result['execution_time'],
            "memory_used": 0,
            "warnings": [],
            "generated_files": []  # 新增：生成的文件信息
        }
        
        # 检查是否生成了图片文件
        chart_dir = "/tmp/matplotlib_charts"
        if os.path.exists(chart_dir):
            import glob
            import base64
            # 查找最新生成的图片文件
            chart_files = glob.glob(os.path.join(chart_dir, "*.png"))
            if chart_files:
                # 按修改时间排序，获取最新的文件
                latest_files = sorted(chart_files, key=os.path.getmtime, reverse=True)
                for filepath in latest_files[:3]:  # 最多返回3个最新文件
                    try:
                        # 读取图片并转换为base64
                        with open(filepath, 'rb') as f:
                            img_data = f.read()
                        img_base64 = base64.b64encode(img_data).decode('utf-8')
                        
                        file_info = {
                            'type': 'image',
                            'format': 'png',
                            'filepath': filepath,
                            'filename': os.path.basename(filepath),
                            'size': len(img_data),
                            'base64': img_base64,
                            'timestamp': os.path.getmtime(filepath)
                        }
                        standardized_result["generated_files"].append(file_info)
                    except Exception as e:
                        print(f"读取图片文件失败: {e}")
        
        # 添加permissive模式的警告
        standardized_result["warnings"].append("使用宽松模式执行，安全检查已放宽")
        if standardized_result["generated_files"]:
            standardized_result["warnings"].append(f"生成了{len(standardized_result['generated_files'])}个图片文件")
        
        return standardized_result
    
    def validate_code(self, code: str) -> dict:
        """
        验证代码但不执行
        
        Args:
            code: 要验证的Python代码字符串
            
        Returns:
            验证结果字典
        """
        try:
            tree = ast.parse(code, '<validation>')
            violations = self.sandbox._validate_ast(tree)
            
            return {
                "valid": len(violations) == 0,
                "violations": violations,
                "syntax_valid": True
            }
        except SyntaxError as e:
            return {
                "valid": False,
                "violations": [f"语法错误: {e.msg} (第 {e.lineno} 行)"],
                "syntax_valid": False
            }
    
    def get_capabilities(self) -> dict:
        """
        返回工具的能力描述
        """
        return {
            "name": "PythonCodeExecutor",
            "description": "安全的Python代码执行工具",
            "version": "1.0.0",
            "supported_modes": ["strict", "normal", "permissive"],
            "limitations": {
                "max_execution_time": self.sandbox.timeout,
                "max_memory_mb": self.sandbox.max_memory_bytes // (1024 * 1024),
                "max_loop_iterations": self.sandbox.max_loop_iterations,
                "max_recursion_depth": self.sandbox.max_recursion_depth
            },
            "security_features": [
                "AST-based code analysis",
                "Memory limitations",
                "Execution timeout",
                "Loop protection",
                "Import restrictions",
                "Dangerous function blocking"
            ]
        }
    
    def _validate_execution_result(self, result: dict) -> list:
        """
        验证执行结果的合理性并返回警告
        """
        warnings = []
        
        # 检查执行时间
        if result["execution_time"] > self.sandbox.timeout * 0.8:
            warnings.append(f"执行时间接近超时限制 ({result['execution_time']:.2f}s)")
        
        # 检查输出长度
        if len(result["output"]) > self.sandbox.max_output_length * 0.8:
            warnings.append("输出长度接近限制")
        
        # 检查输出内容的合理性
        output = result["output"]
        if output:
            # 检查是否有异常的重复输出
            lines = output.split('\n')
            if len(lines) > 100:
                warnings.append("输出行数过多，可能存在意外循环")
            
            # 检查是否有可疑的输出模式
            if any(line.count(char) > 1000 for line in lines for char in line):
                warnings.append("检测到异常重复字符，可能存在输出问题")
        
        # 检查错误信息
        if result["error"]:
            if "RecursionError" in result["error"]:
                warnings.append("递归深度过深，建议使用迭代方法")
            elif "MemoryError" in result["error"]:
                warnings.append("内存使用过高，建议优化算法")
        
        return warnings


# --- 使用示例和测试 ---
if __name__ == "__main__":
    # 测试新的标准化接口
    print("=== 测试标准化接口 ===")
    executor = PythonCodeExecutor()
    
    # 测试能力查询
    capabilities = executor.get_capabilities()
    print(f"工具名称: {capabilities['name']}")
    print(f"版本: {capabilities['version']}")
    print(f"支持模式: {capabilities['supported_modes']}")
    
    # 测试代码验证
    test_code = "print('hello world')"
    validation = executor.validate_code(test_code)
    print(f"\\n代码验证结果: {validation}")
    
    # 测试代码执行
    result = executor.execute_code(test_code, mode="strict")
    print(f"\\n执行结果:")
    print(f"  成功: {result['success']}")
    print(f"  输出: {result['output'].strip()}")
    print(f"  执行时间: {result['execution_time']:.4f}秒")
    print(f"  警告: {result['warnings']}")
    
    # 测试安全库导入
    math_code = """
import math
print(f"π = {math.pi}")
print(f"sin(π/2) = {math.sin(math.pi/2)}")
"""
    print("\\n=== 测试安全库导入 ===")
    result = executor.execute_code(math_code, mode="normal")
    print(f"成功: {result['success']}")
    if result['success']:
        print(f"输出: {result['output'].strip()}")
    else:
        print(f"错误: {result['error']}")
    
    # 测试危险模块导入
    dangerous_code = "import os"
    print("\\n=== 测试危险模块导入 ===")
    result = executor.execute_code(dangerous_code, mode="strict")
    print(f"成功: {result['success']}")
    print(f"错误: {result['error']}")
    
    print("\\n=== 测试原有功能 ===")
    sandbox = SecureSandbox(timeout=5, max_output_length=2000, max_loop_iterations=100000)
    
    def run_test(title, code, **kwargs):
        print(f"=== {title} ===")
        result = sandbox.execute(code, **kwargs)
        if result['stdout']:
            print(f"输出:\n{result['stdout'].strip()}")
        if result['error']:
            # 为了更清晰地显示，只打印错误信息的第一行
            error_line = result['error'].strip().split('\n')[-1]
            print(f"错误: {error_line}")
        else:
            print("错误: None")
        print(f"执行时间: {result['execution_time']:.4f}秒\n")

    # 1. 正常代码
    safe_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

for i in range(8):
    result = fibonacci(i)
    print(f"fib({i}) = {result}")
    
total = sum(range(100))
print(f"Sum of 0-99: {total}")
"""
    run_test("1. 测试正常代码", safe_code, enable_loop_protection=True)

    # 2. 文件访问攻击
    malicious_file = "open('secret.txt', 'w').write('hacked')"
    run_test("2. 测试文件访问攻击", malicious_file)

    # 3. 模块导入攻击
    malicious_import = "import os; os.system('ls')"
    run_test("3. 测试模块导入攻击", malicious_import)

    # 4. 无限循环保护
    infinite_loop = """
count = 0
while True:
    count += 1
print(f"Count reached: {count}")
"""
    run_test("4. 测试无限循环保护 (while)", infinite_loop)

    # 5. 大量for循环保护
    large_loop = """
total = 0
for i in range(200000):
    total += i
print(f"Total: {total}")
"""
    run_test("5. 测试大量for循环保护", large_loop)

    # 6. 语法错误
    syntax_error = "print('hello'"
    run_test("6. 测试语法错误", syntax_error)
    
    # 7. 危险函数调用
    dangerous_func = "eval('print(\"dangerous\")')"
    run_test("7. 测试危险函数调用", dangerous_func)
    
    # 8. 递归深度超限
    deep_recursion = """
def go_deep(n):
    print(f"Depth: {n}")
    go_deep(n+1)

go_deep(1)
"""
    run_test("8. 测试递归深度超限", deep_recursion, enable_loop_protection=False)

    # 9.
    fpint = """
def fibonacci(n):
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        a, b = 0, 1
        for _ in range(2, n+1):
            a, b = b, a + b
        return b

result = fibonacci(10)
print(result)
"""
    run_test("9. 测试递归深度超限", fpint, enable_loop_protection=False)
    