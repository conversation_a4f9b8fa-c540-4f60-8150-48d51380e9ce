#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证号提取测试
"""

from safe_sandbox import PythonCodeExecutor

def test_id_extraction():
    """测试从Excel文件中提取身份证号"""
    
    print("=== 测试Excel文件身份证号提取 ===")
    
    executor = PythonCodeExecutor()
    executor.sandbox.timeout = 20  # 增加超时时间
    
    test_code = '''
import pandas as pd
import re

# 身份证号正则表达式
# 18位身份证：前17位数字 + 最后一位数字或X
id_pattern = r'\\b[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]\\b'

def validate_id_number(id_num):
    """验证身份证号的校验位"""
    if len(id_num) != 18:
        return False
    
    # 权重系数
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    # 校验码对应表
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    try:
        # 计算前17位的加权和
        sum_val = sum(int(id_num[i]) * weights[i] for i in range(17))
        # 计算校验位
        check_index = sum_val % 11
        expected_check = check_codes[check_index]
        
        # 比较校验位（不区分大小写）
        return id_num[17].upper() == expected_check.upper()
    except (ValueError, IndexError):
        return False

def extract_birth_info(id_num):
    """从身份证号提取出生日期和性别信息"""
    if len(id_num) != 18:
        return None, None
    
    try:
        # 提取出生日期
        year = int(id_num[6:10])
        month = int(id_num[10:12])
        day = int(id_num[12:14])
        birth_date = f"{year}-{month:02d}-{day:02d}"
        
        # 提取性别（倒数第二位，奇数为男，偶数为女）
        gender_digit = int(id_num[16])
        gender = "男" if gender_digit % 2 == 1 else "女"
        
        return birth_date, gender
    except (ValueError, IndexError):
        return None, None

print("正在读取Excel文件并提取身份证号...")

try:
    # 读取Excel文件
    df = pd.read_excel('1.xlsx')
    
    print(f"✓ 文件读取成功！数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 重点关注证件号码列
    if '证件号码' in df.columns:
        print(f"\\n找到'证件号码'列，开始提取身份证号...")
        
        id_results = []
        
        for idx, id_value in enumerate(df['证件号码']):
            if pd.notna(id_value):
                id_str = str(id_value).strip()
                
                # 使用正则表达式匹配身份证号
                matches = re.findall(id_pattern, id_str)
                if matches:
                    # 重新匹配完整的身份证号
                    full_matches = re.findall(r'\\b[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]\\b', id_str)
                    for id_num in full_matches:
                        is_valid = validate_id_number(id_num)
                        birth_date, gender = extract_birth_info(id_num)
                        
                        result = {
                            'row': idx + 2,  # Excel行号（从2开始，因为有表头）
                            'name': df.iloc[idx]['被保人姓名'] if '被保人姓名' in df.columns else 'N/A',
                            'id_number': id_num,
                            'is_valid': is_valid,
                            'birth_date': birth_date,
                            'gender': gender,
                            'original_value': id_str
                        }
                        id_results.append(result)
        
        # 输出结果
        print(f"\\n=== 提取结果汇总 ===")
        print(f"总共发现 {len(id_results)} 个身份证号")
        
        if id_results:
            valid_count = sum(1 for r in id_results if r['is_valid'])
            invalid_count = len(id_results) - valid_count
            
            print(f"有效身份证号: {valid_count} 个")
            print(f"无效身份证号: {invalid_count} 个")
            
            print(f"\\n详细信息:")
            for i, result in enumerate(id_results[:10], 1):  # 只显示前10个
                status = "✓ 有效" if result['is_valid'] else "✗ 无效"
                print(f"{i}. 第{result['row']}行 - {result['name']}")
                print(f"   身份证号: {result['id_number']} ({status})")
                if result['birth_date'] and result['gender']:
                    print(f"   出生日期: {result['birth_date']}, 性别: {result['gender']}")
                print()
            
            if len(id_results) > 10:
                print(f"... 还有 {len(id_results) - 10} 个结果未显示")
                
            # 统计性别分布
            gender_stats = {}
            for result in id_results:
                if result['is_valid'] and result['gender']:
                    gender_stats[result['gender']] = gender_stats.get(result['gender'], 0) + 1
            
            if gender_stats:
                print(f"\\n性别分布（仅有效身份证）:")
                for gender, count in gender_stats.items():
                    print(f"  {gender}: {count} 人")
        else:
            print("未发现任何身份证号")
            
            # 显示证件号码列的样本数据
            print(f"\\n证件号码列样本数据（前5行）:")
            for i in range(min(5, len(df))):
                value = df['证件号码'].iloc[i]
                print(f"  第{i+2}行: {repr(value)}")
    else:
        print("未找到'证件号码'列")
        print(f"可用列: {list(df.columns)}")

except Exception as e:
    print(f"处理过程中出错: {e}")
    import traceback
    traceback.print_exc()
'''
    
    result = executor.execute_code(test_code, mode="permissive")
    
    print("执行结果:")
    print(f"成功: {result['success']}")
    print(f"执行时间: {result['execution_time']:.2f}秒")
    
    if result['success']:
        print("\\n输出:")
        print(result['output'])
    else:
        print("\\n错误:")
        print(result['error'])
    
    if result['warnings']:
        print("\\n警告:")
        for warning in result['warnings']:
            print(f"  - {warning}")

if __name__ == "__main__":
    test_id_extraction()
