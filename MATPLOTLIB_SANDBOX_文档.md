# Python Matplotlib 沙盒执行器完整文档

## 📋 项目概述

这是一个完整的Python代码沙盒执行器，特别优化了matplotlib图表生成功能，支持中文字体显示和自动图片保存。项目包含安全的代码执行环境、LLM Function Calling集成以及完整的图表可视化支持。

## 🏗️ 核心架构

### 主要组件

1. **safe_sandbox.py** - 核心安全沙盒执行器
2. **llm_sandbox.py** - LLM Function Calling集成
3. **mcp_python_executor.py** - MCP(Model Context Protocol)工具封装

### 安全特性

- ✅ 沙盒化代码执行环境
- ✅ 内存使用限制 (可配置MB)
- ✅ 执行时间限制 (可配置秒数)
- ✅ 循环迭代次数限制
- ✅ 危险模块导入阻止
- ✅ 文件IO操作限制
- ✅ 网络访问限制

## 🎨 Matplotlib 功能特性

### 1. 中文字体支持

**自动字体检测与配置**:
- **macOS**: Hiragino Sans GB, PingFang SC
- **Windows**: Microsoft YaHei, SimHei  
- **Linux**: DejaVu Sans, WenQuanYi

**使用约束规则**:
```python
# ✅ 正确做法 - 直接使用中文
plt.title('中国主要城市人口统计')
plt.xlabel('城市名称')
plt.ylabel('人口数量（万人）')

# ❌ 避免做法 - 手动设置字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 不要这样做
```

### 2. 自动图片保存

**智能触发机制**:
- `plt.close()` - 关闭图表时自动保存
- `plt.show()` - 显示图表时保存
- 代码执行完成时自动保存

**文件规格**:
- 格式: PNG (150 DPI高质量)
- 路径: `/tmp/matplotlib_charts/`
- 命名: `chart_{counter}_{YYYYMMDD}_{HHMMSS}_{ms}.png`
- 返回: 文件路径 + Base64编码

### 3. 支持的图表类型

✅ **柱状图** - 中文标题、轴标签、数据标签
✅ **折线图** - 中文图例、标签  
✅ **饼图** - 中文分类标签
✅ **散点图** - 中文坐标轴标签
✅ **直方图** - 中文统计标签
✅ **热力图** - 中文坐标和标题

## 🔧 执行模式

### 三种安全级别

1. **strict** - 最严格模式
   - 禁用大部分标准库
   - 最小循环限制
   - 适用于基本计算

2. **normal** - 标准模式  
   - 平衡安全性和功能性
   - 允许常用标准库
   - 适用于数据处理

3. **permissive** - 宽松模式
   - 允许可视化库 (matplotlib, seaborn等)
   - 启用中文字体支持
   - 启用图片自动保存
   - **推荐用于图表生成**

## 📊 使用示例

### 基本用法

```python
from safe_sandbox import PythonCodeExecutor

config = {
    'timeout': 30,
    'max_loop_iterations': 100000,
    'max_memory_mb': 100,
    'max_output_length': 20000
}
executor = PythonCodeExecutor(config)

# 生成中文图表
matplotlib_code = """
import matplotlib.pyplot as plt

cities = ['北京', '上海', '广州', '深圳', '杭州']
population = [2154, 2424, 1532, 1344, 1036]

plt.figure(figsize=(10, 6))
plt.bar(cities, population, color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum'])
plt.title('中国主要城市人口统计')
plt.xlabel('城市名称')  
plt.ylabel('人口数量（万人）')

print("✅ 中文图表生成完成！")
plt.close()
"""

result = executor.execute_code(matplotlib_code, mode='permissive')
```

### 执行结果结构

```json
{
  "success": true,
  "output": "✅ 已设置中文字体: Hiragino Sans GB\n📊 图片自动保存功能已启用\n✅ 中文图表生成完成！\n📊 图片已保存: /tmp/matplotlib_charts/chart_1_20250722_130505_735.png",
  "execution_time": 0.4523,
  "warnings": ["使用宽松模式执行，安全检查已放宽", "生成了1个图片文件"],
  "generated_files": [
    {
      "type": "image",
      "format": "png", 
      "filepath": "/tmp/matplotlib_charts/chart_1_20250722_130505_735.png",
      "filename": "chart_1_20250722_130505_735.png",
      "size": 40892,
      "base64": "iVBORw0KGgoAAAANSUhEUgAAAoAAAAH...",
      "timestamp": 1721621105.735
    }
  ]
}
```

## 🤖 LLM Function Calling 集成

### Function Schema

```json
{
  "type": "function",
  "function": {
    "name": "execute_python_code",
    "description": "安全地执行Python代码并返回结果。支持基本计算、数据处理和安全的标准库使用。可以生成图表并自动保存为图片文件。",
    "parameters": {
      "type": "object", 
      "properties": {
        "code": {
          "type": "string",
          "description": "要执行的Python代码字符串"
        },
        "mode": {
          "type": "string",
          "enum": ["strict", "normal", "permissive"],
          "default": "normal",
          "description": "执行模式：strict(最严格), normal(标准模式), permissive(允许更多标准库)"
        },
        "description": {
          "type": "string", 
          "description": "对要执行的代码的简要描述"
        }
      },
      "required": ["code"]
    }
  }
}
```

### 系统提示词约束

```
MATPLOTLIB中文显示重要规则：
1. 生成图表时，系统已自动配置中文字体支持，无需手动设置字体
2. 直接使用中文文字作为标题、标签等，系统会自动处理字体
3. 不要添加任何字体设置代码，如plt.rcParams、font等
4. 不要使用matplotlib.use()或设置backend，系统已自动配置
5. 示例正确用法：
   ```python
   plt.title('中文标题')
   plt.xlabel('中文X轴标签') 
   plt.ylabel('中文Y轴标签')
   ```
6. 生成的图表会自动保存为PNG文件，用户可以查看

代码生成最佳实践：
- 使用permissive模式处理matplotlib代码
- 保持代码简洁，让系统自动处理字体和保存
- 在plt.close()前添加print语句说明图表已生成
- 使用合适的颜色和图表尺寸
```

## 🛠️ MCP 工具封装

### 作为MCP工具使用

```python
# mcp_python_executor.py 提供了完整的MCP工具封装
# 可以直接集成到支持MCP的LLM应用中

import asyncio
from mcp import server, types
from safe_sandbox import PythonCodeExecutor

# 工具会自动处理代码执行、图片生成和结果返回
```

## 📈 技术优势

### 1. 安全性
- 完整的沙盒环境，隔离危险操作
- 多层安全检查和验证
- 资源使用限制保护

### 2. 功能性  
- 支持完整的matplotlib功能
- 自动中文字体配置
- 多种图表类型支持
- 高质量图片输出

### 3. 易用性
- 零配置开箱即用
- 自动处理复杂字体设置  
- 清晰的错误提示和警告
- 详细的执行结果返回

### 4. 兼容性
- 跨平台字体支持 (macOS/Windows/Linux)
- 兼容现有matplotlib代码
- 支持多种LLM集成方式
- MCP标准协议支持

## 🎯 最佳实践

### 图表生成建议

1. **始终使用permissive模式**进行matplotlib操作
2. **直接使用中文文字**，不要手动设置字体
3. **适当的图表尺寸** `plt.figure(figsize=(10, 6))`
4. **添加说明信息** 在`plt.close()`前打印完成信息
5. **使用合适的颜色方案**提升图表可读性

### 错误处理

```python
result = executor.execute_code(code, mode='permissive')

if result['success']:
    print(f"✅ 执行成功: {result['output']}")
    
    if result.get('generated_files'):
        for file_info in result['generated_files']:
            print(f"📊 生成图片: {file_info['filepath']}")
            print(f"📊 文件大小: {file_info['size']} bytes")
else:
    print(f"❌ 执行失败: {result['error']}")
    if result.get('violations'):
        print(f"🚫 安全违规: {result['violations']}")
```

## 🔍 故障排除

### 常见问题

1. **中文显示为方框**
   - 确认使用permissive模式
   - 检查系统中文字体安装

2. **图片未生成**  
   - 确认使用permissive模式
   - 检查代码中是否有plt.close()

3. **内存超限**
   - 调整max_memory_mb配置
   - 简化图表复杂度

4. **执行超时**
   - 增加timeout配置值
   - 优化代码性能

### 调试信息

执行时会显示详细的调试信息：
```
✅ 已设置中文字体: Hiragino Sans GB
📊 图片自动保存功能已启用  
📊 图片已保存: /tmp/matplotlib_charts/chart_1_xxx.png
📊 图片大小: 40892 bytes (base64)
```

## 📁 文件说明

### 核心文件
- `safe_sandbox.py` - 安全沙盒执行器核心实现
- `llm_sandbox.py` - LLM Function Calling集成示例  
- `mcp_python_executor.py` - MCP工具协议封装

### 文档文件
- `MATPLOTLIB_SANDBOX_文档.md` - 本文档（完整说明）

这个Python沙盒执行器为LLM提供了安全、强大且易用的代码执行能力，特别是在数据可视化和图表生成方面提供了卓越的中文支持体验！