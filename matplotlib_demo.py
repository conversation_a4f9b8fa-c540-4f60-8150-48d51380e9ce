#!/usr/bin/env python3
"""
matplotlib功能演示脚本 - 展示 @llm_sandbox.py 的matplotlib能力
"""
 
import json
import os
import sys
from openai import OpenAI

# 模拟Function Calling的结果展示
def simulate_matplotlib_demo():
    """
    模拟展示matplotlib在llm_sandbox中的使用效果
    """
    
    print("🎨 matplotlib 图表生成能力演示")
    print("=" * 60)
    
    # 模拟的测试用例和预期结果
    matplotlib_demos = [
        {
            "用户请求": "使用matplotlib生成一个简单的折线图，显示x=0到10，y=x²的函数图像",
            "生成代码": """
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt

# 生成数据
x = list(range(0, 11))
y = [i**2 for i in x]

# 创建图形
plt.figure(figsize=(8, 6))
plt.plot(x, y, 'b-', linewidth=2)
plt.title('y = x² 函数图像')
plt.xlabel('x')
plt.ylabel('y')
plt.grid(True)

print("✅ 折线图创建成功！")
print(f"数据点: x={x[:5]}..., y={y[:5]}...")
plt.close()
""",
            "预期输出": "✅ 折线图创建成功！\n数据点: x=[0, 1, 2, 3, 4]..., y=[0, 1, 4, 9, 16]...",
            "功能特点": "二次函数可视化，网格显示，标题和轴标签"
        },
        {
            "用户请求": "创建一个柱状图显示5个城市的人口数据",
            "生成代码": """
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

cities = ['北京', '上海', '广州', '深圳', '杭州']
population = [2154, 2424, 1532, 1344, 1036]

plt.figure(figsize=(10, 6))
plt.bar(cities, population, color=['red', 'blue', 'green', 'orange', 'purple'])
plt.title('中国主要城市人口统计')
plt.xlabel('城市')
plt.ylabel('人口(万)')
plt.xticks(rotation=45)

print("✅ 柱状图创建成功！")
for city, pop in zip(cities, population):
    print(f"{city}: {pop}万人")
plt.close()
""",
            "预期输出": "✅ 柱状图创建成功！\n北京: 2154万人\n上海: 2424万人\n广州: 1532万人\n深圳: 1344万人\n杭州: 1036万人",
            "功能特点": "多色柱状图，中文标签支持，数据统计显示"
        },
        {
            "用户请求": "生成一个散点图，显示20个随机点的分布",
            "生成代码": """
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import random

# 生成随机数据
random.seed(42)
x = [random.uniform(0, 100) for _ in range(20)]
y = [random.uniform(0, 100) for _ in range(20)]

plt.figure(figsize=(8, 8))
plt.scatter(x, y, c='red', alpha=0.6, s=100)
plt.title('20个随机点的散点图')
plt.xlabel('X坐标')
plt.ylabel('Y坐标')
plt.grid(True, alpha=0.3)

print("✅ 散点图创建成功！")
print(f"显示了{len(x)}个随机点")
plt.close()
""",
            "预期输出": "✅ 散点图创建成功！\n显示了20个随机点",
            "功能特点": "随机数据可视化，透明度设置，网格背景"
        },
        {
            "用户请求": "使用matplotlib画一个饼图显示编程语言使用率",
            "生成代码": """
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

languages = ['Python', 'JavaScript', 'Java', 'C++', '其他']
usage = [40, 25, 20, 10, 5]
colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']

plt.figure(figsize=(8, 8))
plt.pie(usage, labels=languages, colors=colors, autopct='%1.1f%%', startangle=90)
plt.title('编程语言使用率分布')
plt.axis('equal')

print("✅ 饼图创建成功！")
for lang, percent in zip(languages, usage):
    print(f"{lang}: {percent}%")
plt.close()
""",
            "预期输出": "✅ 饼图创建成功！\nPython: 40%\nJavaScript: 25%\nJava: 20%\nC++: 10%\n其他: 5%",
            "功能特点": "百分比显示，自定义颜色，等比例圆形"
        }
    ]
    
    for i, demo in enumerate(matplotlib_demos, 1):
        print(f"\n📊 演示 {i}: {demo['用户请求']}")
        print("-" * 50)
        
        print("🤖 LLM会生成的代码:")
        print(f"```python\n{demo['生成代码'].strip()}\n```")
        
        print("\n✅ 预期执行结果:")
        print(f"   成功: True")
        print(f"   输出: {demo['预期输出']}")
        print(f"   功能特点: {demo['功能特点']}")
        print(f"   执行时间: ~0.1-0.5秒")
    
    print("\n" + "=" * 60)
    print("🎉 matplotlib功能特性总结:")
    print("   ✅ 支持多种图表类型：折线图、柱状图、散点图、饼图")
    print("   ✅ 中文标签和文字支持")
    print("   ✅ 自定义颜色和样式")
    print("   ✅ 安全的非交互式后端（Agg）")
    print("   ✅ 完整的图表元素：标题、轴标签、网格、图例")
    print("   ✅ 与数学计算和随机数生成结合")
    
    print("\n🔧 技术实现:")
    print("   • matplotlib添加到安全库白名单")
    print("   • 支持matplotlib.pyplot子模块导入")
    print("   • 自动使用Agg后端避免GUI依赖")
    print("   • 内存和执行时间限制保障安全性")
    
    print("\n💡 MCP工具集成:")
    print("   • Function Schema包含matplotlib支持")
    print("   • 执行模式：permissive（允许可视化库）")
    print("   • 标准化JSON响应格式")
    print("   • 完整的错误处理和验证")

if __name__ == "__main__":
    simulate_matplotlib_demo()