#!/usr/bin/env python3
"""
MCP Python代码执行工具
基于安全沙箱的Python代码执行服务

使用方法:
1. 导入此模块
2. 使用PYTHON_EXECUTOR_TOOL作为MCP工具schema
3. 使用handle_python_execution作为处理函数
"""

import json
from safe_sandbox import PythonCodeExecutor

# MCP工具定义
PYTHON_EXECUTOR_TOOL = {
    "name": "execute_python_code",
    "description": "安全地执行Python代码并返回结果。支持基本计算、数据处理和安全的标准库使用。",
    "inputSchema": {
        "type": "object",
        "properties": {
            "code": {
                "type": "string",
                "description": "要执行的Python代码字符串"
            },
            "mode": {
                "type": "string",
                "enum": ["strict", "normal", "permissive"],
                "default": "normal",
                "description": "执行模式：strict(最严格), normal(标准模式), permissive(允许更多标准库)"
            },
            "description": {
                "type": "string",
                "description": "对要执行的代码的简要描述，用于日志记录"
            }
        },
        "required": ["code"]
    }
}

class MCPPythonExecutor:
    """MCP Python代码执行工具类"""
    
    def __init__(self, config=None):
        """
        初始化MCP Python执行器
        
        Args:
            config: 配置字典，包含沙箱参数
        """
        default_config = {
            'timeout': 10,
            'max_loop_iterations': 100000,
            'max_memory_mb': 50,
            'max_output_length': 10000
        }
        if config:
            default_config.update(config)
        
        self.executor = PythonCodeExecutor(default_config)
        self.config = default_config
    
    def handle_python_execution(self, arguments: dict) -> dict:
        """
        MCP工具调用处理函数
        
        Args:
            arguments: 包含code, mode, description的字典
            
        Returns:
            MCP标准响应格式
        """
        try:
            code = arguments.get("code", "")
            mode = arguments.get("mode", "normal")
            description = arguments.get("description", "")
            
            if not code.strip():
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": "错误：代码内容不能为空"
                        }
                    ],
                    "isError": True
                }
            
            # 1. 代码验证
            validation = self.executor.validate_code(code)
            if not validation['valid']:
                error_msg = "代码安全验证失败：\n" + "\n".join(f"- {v}" for v in validation['violations'])
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": error_msg
                        }
                    ],
                    "isError": True
                }
            
            # 2. 执行代码
            result = self.executor.execute_code(code, mode=mode)
            
            # 3. 格式化响应
            response_parts = []
            
            if result['success']:
                if result['output']:
                    response_parts.append(f"✅ 执行成功\n\n**输出：**\n```\n{result['output'].strip()}\n```")
                else:
                    response_parts.append("✅ 执行成功（无输出）")
            else:
                response_parts.append(f"❌ 执行失败\n\n**错误：**\n{result['error']}")
            
            # 添加执行信息
            info_parts = [
                f"执行模式: {mode}",
                f"执行时间: {result['execution_time']:.4f}秒"
            ]
            
            if result.get('warnings'):
                info_parts.append(f"警告: {', '.join(result['warnings'])}")
            
            response_parts.append(f"\n**执行信息：**\n{chr(10).join(info_parts)}")
            
            return {
                "content": [
                    {
                        "type": "text",
                        "text": "\n\n".join(response_parts)
                    }
                ],
                "isError": not result['success']
            }
            
        except Exception as e:
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"MCP工具内部错误：{str(e)}"
                    }
                ],
                "isError": True
            }
    
    def get_tool_info(self) -> dict:
        """返回工具信息"""
        capabilities = self.executor.get_capabilities()
        return {
            "tool": PYTHON_EXECUTOR_TOOL,
            "capabilities": capabilities,
            "config": self.config
        }

# 全局实例（可选）
default_executor = MCPPythonExecutor()

def handle_python_execution(arguments: dict) -> dict:
    """便捷的全局处理函数"""
    return default_executor.handle_python_execution(arguments)

# 使用示例
if __name__ == "__main__":
    # 创建执行器实例
    mcp_executor = MCPPythonExecutor()
    
    # 获取工具信息
    tool_info = mcp_executor.get_tool_info()
    print("MCP工具信息：")
    print(json.dumps(tool_info["tool"], indent=2, ensure_ascii=False))
    
    # 测试执行
    test_cases = [
        {
            "code": "print('Hello, MCP!')",
            "description": "简单输出测试"
        },
        {
            "code": "import math\nprint(f'π = {math.pi}')",
            "mode": "permissive",
            "description": "数学库测试"
        },
        {
            "code": "import os\nos.listdir('.')",
            "description": "危险操作测试"
        }
    ]
    
    print("\n测试用例：")
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i} ---")
        result = mcp_executor.handle_python_execution(test_case)
        print(result["content"][0]["text"])