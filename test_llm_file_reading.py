#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM沙箱的文件读取功能
"""

import sys
import os

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from safe_sandbox import PythonCodeExecutor

def test_llm_generated_code():
    """测试模拟LLM生成的代码来读取Excel文件并提取身份证号"""
    
    print("=== 测试LLM生成的Excel身份证号提取代码 ===")
    
    # 创建执行器
    executor = PythonCodeExecutor()
    executor.sandbox.timeout = 20  # 增加超时时间
    
    # 模拟LLM生成的代码（这是LLM可能会生成的代码）
    llm_generated_code = '''
import pandas as pd
import re

print("🔍 开始分析Excel文件中的身份证号码...")

# 身份证号正则表达式（18位）
id_pattern = r'\\b[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]\\b'

def validate_id_checksum(id_num):
    """验证身份证号校验位"""
    if len(id_num) != 18:
        return False
    
    # 权重系数
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    # 校验码对应表
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    try:
        # 计算前17位的加权和
        sum_val = sum(int(id_num[i]) * weights[i] for i in range(17))
        # 计算校验位
        check_index = sum_val % 11
        expected_check = check_codes[check_index]
        
        # 比较校验位（不区分大小写）
        return id_num[17].upper() == expected_check.upper()
    except (ValueError, IndexError):
        return False

def extract_personal_info(id_num):
    """从身份证号提取个人信息"""
    if len(id_num) != 18:
        return None, None, None
    
    try:
        # 提取出生日期
        year = int(id_num[6:10])
        month = int(id_num[10:12])
        day = int(id_num[12:14])
        birth_date = f"{year}-{month:02d}-{day:02d}"
        
        # 提取性别（倒数第二位，奇数为男，偶数为女）
        gender_digit = int(id_num[16])
        gender = "男" if gender_digit % 2 == 1 else "女"
        
        # 计算年龄（简单计算）
        from datetime import datetime
        current_year = datetime.now().year
        age = current_year - year
        
        return birth_date, gender, age
    except (ValueError, IndexError):
        return None, None, None

try:
    # 1. 读取Excel文件
    print("\\n📊 步骤1: 读取Excel文件")
    df = pd.read_excel('1.xlsx')
    
    print(f"✅ 文件读取成功！")
    print(f"   数据形状: {df.shape[0]} 行 × {df.shape[1]} 列")
    print(f"   列名: {list(df.columns)}")
    
    # 2. 搜索身份证号码
    print("\\n🔍 步骤2: 搜索身份证号码")
    
    id_results = []
    
    # 遍历所有列查找身份证号
    for col_name in df.columns:
        print(f"   检查列: {col_name}")
        
        for row_idx, cell_value in enumerate(df[col_name]):
            if pd.notna(cell_value):
                cell_str = str(cell_value).strip()
                
                # 使用正则表达式查找身份证号
                matches = re.findall(id_pattern, cell_str)
                if matches:
                    # 重新匹配完整的身份证号
                    full_ids = re.findall(r'\\b[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]\\b', cell_str)
                    
                    for id_num in full_ids:
                        # 验证校验位
                        is_valid = validate_id_checksum(id_num)
                        
                        # 提取个人信息
                        birth_date, gender, age = extract_personal_info(id_num)
                        
                        # 获取姓名（如果有）
                        name = "未知"
                        if '被保人姓名' in df.columns:
                            name = str(df.iloc[row_idx]['被保人姓名']) if pd.notna(df.iloc[row_idx]['被保人姓名']) else "未知"
                        
                        result = {
                            'row': row_idx + 2,  # Excel行号（从2开始）
                            'column': col_name,
                            'name': name,
                            'id_number': id_num,
                            'is_valid': is_valid,
                            'birth_date': birth_date,
                            'gender': gender,
                            'age': age,
                            'original_text': cell_str[:50] + ('...' if len(cell_str) > 50 else '')
                        }
                        id_results.append(result)
                        
                        status = "✅ 有效" if is_valid else "❌ 无效"
                        print(f"     第{row_idx+2}行发现: {id_num} ({status})")
    
    # 3. 验证和统计结果
    print("\\n📈 步骤3: 验证和统计结果")
    
    total_found = len(id_results)
    valid_count = sum(1 for r in id_results if r['is_valid'])
    invalid_count = total_found - valid_count
    
    print(f"\\n📊 统计结果:")
    print(f"   总共发现: {total_found} 个身份证号")
    print(f"   有效数量: {valid_count} 个")
    print(f"   无效数量: {invalid_count} 个")
    print(f"   有效率: {(valid_count/total_found*100):.1f}%" if total_found > 0 else "   有效率: 0%")
    
    # 4. 显示详细信息
    if id_results:
        print("\\n📋 步骤4: 详细信息")
        
        # 显示前10个结果
        display_count = min(10, len(id_results))
        print(f"\\n详细信息（显示前{display_count}个）:")
        
        for i, result in enumerate(id_results[:display_count], 1):
            status = "✅" if result['is_valid'] else "❌"
            print(f"\\n{i}. {status} 第{result['row']}行 - {result['name']}")
            print(f"   身份证号: {result['id_number']}")
            print(f"   所在列: {result['column']}")
            
            if result['is_valid'] and result['birth_date']:
                print(f"   出生日期: {result['birth_date']}")
                print(f"   性别: {result['gender']}")
                print(f"   年龄: {result['age']}岁")
            
            print(f"   原始数据: {result['original_text']}")
        
        if len(id_results) > display_count:
            print(f"\\n... 还有 {len(id_results) - display_count} 个结果未显示")
        
        # 5. 性别和年龄统计
        if valid_count > 0:
            print("\\n📊 步骤5: 人员统计（仅统计有效身份证）")
            
            # 性别统计
            gender_stats = {}
            age_list = []
            
            for result in id_results:
                if result['is_valid']:
                    if result['gender']:
                        gender_stats[result['gender']] = gender_stats.get(result['gender'], 0) + 1
                    if result['age']:
                        age_list.append(result['age'])
            
            print("\\n性别分布:")
            for gender, count in gender_stats.items():
                percentage = (count / valid_count) * 100
                print(f"   {gender}: {count} 人 ({percentage:.1f}%)")
            
            if age_list:
                print("\\n年龄统计:")
                print(f"   平均年龄: {sum(age_list)/len(age_list):.1f} 岁")
                print(f"   最小年龄: {min(age_list)} 岁")
                print(f"   最大年龄: {max(age_list)} 岁")
    else:
        print("\\n❌ 未发现任何身份证号码")
        
        # 显示样本数据用于调试
        print("\\n🔍 样本数据（前3行，前3列）:")
        for i in range(min(3, len(df))):
            for j, col in enumerate(df.columns[:3]):
                value = df.iloc[i, j]
                print(f"   [{i+2}, {col}]: {repr(value)}")

except Exception as e:
    print(f"\\n❌ 处理过程中出错: {e}")
    import traceback
    traceback.print_exc()

print("\\n✅ 分析完成！")
'''
    
    print("执行LLM生成的代码...")
    result = executor.execute_code(llm_generated_code, mode="permissive")
    
    print(f"\\n执行结果:")
    print(f"成功: {result['success']}")
    print(f"执行时间: {result['execution_time']:.2f}秒")
    
    if result['success']:
        print("\\n输出:")
        print(result['output'])
    else:
        print("\\n错误:")
        print(result['error'])
    
    if result['warnings']:
        print("\\n警告:")
        for warning in result['warnings']:
            print(f"  - {warning}")

def test_safe_read_file_function():
    """测试safe_read_file函数的使用"""
    
    print("\\n\\n=== 测试safe_read_file函数 ===")
    
    executor = PythonCodeExecutor()
    
    test_code = '''
print("测试safe_read_file函数...")

# 测试读取Excel文件
try:
    content = safe_read_file("1.xlsx")
    print(f"✅ safe_read_file读取成功")
    print(f"内容类型: {type(content)}")
    print(f"内容: {content}")
except Exception as e:
    print(f"❌ safe_read_file读取失败: {e}")

# 测试安全限制
print("\\n测试安全限制...")
try:
    content = safe_read_file("../etc/passwd")
    print("❌ 安全检查失败！")
except Exception as e:
    print(f"✅ 正确拒绝危险路径: {type(e).__name__}")
'''
    
    result = executor.execute_code(test_code, mode="normal")
    
    print(f"执行结果:")
    print(f"成功: {result['success']}")
    
    if result['success']:
        print("输出:")
        print(result['output'])
    else:
        print("错误:")
        print(result['error'])

if __name__ == "__main__":
    test_safe_read_file_function()
    test_llm_generated_code()
