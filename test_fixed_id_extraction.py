#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版本的身份证号提取测试
"""

import sys
import os

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from safe_sandbox import PythonCodeExecutor

def test_fixed_id_extraction():
    """测试修复后的身份证号提取代码"""
    
    print("=== 测试修复后的身份证号提取 ===")
    
    executor = PythonCodeExecutor()
    executor.sandbox.timeout = 20
    
    # 修复后的代码
    fixed_code = '''
import pandas as pd
import re

print("🔍 开始分析Excel文件中的身份证号码...")

# 修复后的身份证号正则表达式
id_pattern = r'\\b[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]\\b'

def validate_id_checksum(id_num):
    """验证身份证号校验位"""
    if len(id_num) != 18:
        return False
    
    # 权重系数
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    # 校验码对应表
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    try:
        # 计算前17位的加权和
        sum_val = sum(int(id_num[i]) * weights[i] for i in range(17))
        # 计算校验位
        check_index = sum_val % 11
        expected_check = check_codes[check_index]
        
        # 比较校验位（不区分大小写）
        return id_num[17].upper() == expected_check.upper()
    except (ValueError, IndexError):
        return False

def extract_personal_info(id_num):
    """从身份证号提取个人信息"""
    if len(id_num) != 18:
        return None, None, None
    
    try:
        # 提取出生日期
        year = int(id_num[6:10])
        month = int(id_num[10:12])
        day = int(id_num[12:14])
        birth_date = f"{year}-{month:02d}-{day:02d}"
        
        # 提取性别（倒数第二位，奇数为男，偶数为女）
        gender_digit = int(id_num[16])
        gender = "男" if gender_digit % 2 == 1 else "女"
        
        # 计算年龄（简单计算）
        from datetime import datetime
        current_year = datetime.now().year
        age = current_year - year
        
        return birth_date, gender, age
    except (ValueError, IndexError):
        return None, None, None

try:
    # 读取Excel文件
    print("\\n📊 读取Excel文件")
    df = pd.read_excel('1.xlsx')
    
    print(f"✅ 文件读取成功！")
    print(f"   数据形状: {df.shape[0]} 行 × {df.shape[1]} 列")
    
    # 重点检查证件号码列
    if '证件号码' in df.columns:
        print("\\n🔍 分析证件号码列")
        
        id_results = []
        
        for row_idx, cell_value in enumerate(df['证件号码']):
            if pd.notna(cell_value):
                cell_str = str(cell_value).strip()
                
                # 使用re.finditer来获取完整匹配
                for match in re.finditer(id_pattern, cell_str):
                    id_num = match.group()  # 获取完整匹配的字符串
                    
                    # 验证校验位
                    is_valid = validate_id_checksum(id_num)
                    
                    # 提取个人信息
                    birth_date, gender, age = extract_personal_info(id_num)
                    
                    # 获取姓名
                    name = str(df.iloc[row_idx]['被保人姓名']) if pd.notna(df.iloc[row_idx]['被保人姓名']) else "未知"
                    
                    result = {
                        'row': row_idx + 2,  # Excel行号
                        'name': name,
                        'id_number': id_num,
                        'is_valid': is_valid,
                        'birth_date': birth_date,
                        'gender': gender,
                        'age': age,
                        'original_text': cell_str
                    }
                    id_results.append(result)
                    
                    status = "✅ 有效" if is_valid else "❌ 无效"
                    print(f"   第{row_idx+2}行 {name}: {id_num} ({status})")
        
        # 统计结果
        total_found = len(id_results)
        valid_count = sum(1 for r in id_results if r['is_valid'])
        invalid_count = total_found - valid_count
        
        print(f"\\n📊 统计结果:")
        print(f"   总共发现: {total_found} 个身份证号")
        print(f"   有效数量: {valid_count} 个")
        print(f"   无效数量: {invalid_count} 个")
        print(f"   有效率: {(valid_count/total_found*100):.1f}%" if total_found > 0 else "   有效率: 0%")
        
        # 显示有效身份证的详细信息
        if valid_count > 0:
            print(f"\\n📋 有效身份证详细信息:")
            
            valid_results = [r for r in id_results if r['is_valid']]
            
            for i, result in enumerate(valid_results[:10], 1):  # 显示前10个
                print(f"\\n{i}. ✅ 第{result['row']}行 - {result['name']}")
                print(f"   身份证号: {result['id_number']}")
                print(f"   出生日期: {result['birth_date']}")
                print(f"   性别: {result['gender']}")
                print(f"   年龄: {result['age']}岁")
            
            if len(valid_results) > 10:
                print(f"\\n... 还有 {len(valid_results) - 10} 个有效结果未显示")
            
            # 性别和年龄统计
            print("\\n📊 人员统计（仅统计有效身份证）:")
            
            gender_stats = {}
            age_list = []
            
            for result in valid_results:
                if result['gender']:
                    gender_stats[result['gender']] = gender_stats.get(result['gender'], 0) + 1
                if result['age']:
                    age_list.append(result['age'])
            
            print("\\n性别分布:")
            for gender, count in gender_stats.items():
                percentage = (count / valid_count) * 100
                print(f"   {gender}: {count} 人 ({percentage:.1f}%)")
            
            if age_list:
                print("\\n年龄统计:")
                print(f"   平均年龄: {sum(age_list)/len(age_list):.1f} 岁")
                print(f"   最小年龄: {min(age_list)} 岁")
                print(f"   最大年龄: {max(age_list)} 岁")
        
        # 显示无效身份证的样本（用于调试）
        if invalid_count > 0:
            print(f"\\n❌ 无效身份证样本（前5个）:")
            invalid_results = [r for r in id_results if not r['is_valid']]
            
            for i, result in enumerate(invalid_results[:5], 1):
                print(f"   {i}. {result['name']}: {result['id_number']}")
                
                # 简单分析为什么无效
                id_num = result['id_number']
                if len(id_num) != 18:
                    print(f"      原因: 长度不是18位 (实际{len(id_num)}位)")
                else:
                    print(f"      原因: 校验位不正确")
    else:
        print("❌ 未找到'证件号码'列")

except Exception as e:
    print(f"\\n❌ 处理过程中出错: {e}")
    import traceback
    traceback.print_exc()

print("\\n✅ 分析完成！")
'''
    
    print("执行修复后的代码...")
    result = executor.execute_code(fixed_code, mode="permissive")
    
    print(f"\\n执行结果:")
    print(f"成功: {result['success']}")
    print(f"执行时间: {result['execution_time']:.2f}秒")
    
    if result['success']:
        print("\\n输出:")
        print(result['output'])
    else:
        print("\\n错误:")
        print(result['error'])

if __name__ == "__main__":
    test_fixed_id_extraction()
