import json
import os
import sys
from openai import OpenAI

# 定义Python代码执行工具的Function Schema
PYTHON_EXECUTOR_FUNCTION = {
    "type": "function",
    "function": {
        "name": "execute_python_code",
        "description": "安全地执行Python代码并返回结果。支持基本计算、数据处理、安全的标准库使用、本地文件读取和数据分析。可以生成图表并自动保存为图片文件。内置safe_read_file()函数用于安全读取当前目录下的文件。",
        "parameters": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "description": "要执行的Python代码字符串"
                },
                "mode": {
                    "type": "string",
                    "enum": ["strict", "normal", "permissive"],
                    "default": "normal",
                    "description": "执行模式：strict(最严格，禁用循环保护), normal(标准模式), permissive(允许更多标准库)"
                },
                "description": {
                    "type": "string",
                    "description": "对要执行的代码的简要描述，用于日志记录"
                }
            },
            "required": ["code"]
        }
    }
}

def execute_python_code_function(code: str, mode: str = "normal", description: str = "") -> dict:
    """
    Function Call处理函数：执行Python代码
    
    Args:
        code: Python代码字符串
        mode: 执行模式
        description: 代码描述
    
    Returns:
        标准化的执行结果
    """
    global executor
    
    print(f"\n🔧 Function Call: execute_python_code")
    print(f"   描述: {description or '用户请求的代码执行'}")
    print(f"   模式: {mode}")
    print(f"   代码: {code[:100]}{'...' if len(code) > 100 else ''}")
    
    # 1. 代码验证
    validation = executor.validate_code(code)
    if not validation['valid']:
        return {
            "success": False,
            "error": "代码安全验证失败",
            "violations": validation['violations'],
            "executed": False
        }
    
    # 2. 执行代码
    result = executor.execute_code(code, mode=mode)
    
    # 3. 返回结构化结果
    return {
        "success": result['success'],
        "output": result['output'],
        "error": result['error'],
        "execution_time": result['execution_time'],
        "warnings": result['warnings'],
        "mode_used": mode,
        "executed": True
    }

# 将当前目录添加到系统路径以便导入 SecureSandbox
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # --- 诊断代码开始 ---
    # 首先导入整个模块，以便我们可以检查它的来源
    import safe_sandbox
    # 打印出实际加载的模块文件的路径
    print(f"✅ 成功从以下路径加载沙箱模块: {safe_sandbox.__file__}")
    # --- 诊断代码结束 ---

    # 从我们之前创建的文件中导入标准化的代码执行器
    from safe_sandbox import PythonCodeExecutor, SecurityError
except ImportError:
    print("错误: 无法找到 'safe_sandbox.py'。")
    print("请确保 'safe_sandbox.py' 和本测试脚本在同一个目录下。")
    sys.exit(1)

# 全局执行器实例
executor = None


def chat_with_function_calls(client: OpenAI, user_message: str) -> None:
    """
    与LLM进行支持function calling的对话
    
    Args:
        client: OpenAI客户端
        user_message: 用户消息
    """
    messages = [
        {
            "role": "system", 
            "content": """你是一个Python编程助手，可以安全地执行Python代码来帮助用户。

你有一个Python代码执行工具，可以：
- 执行基本的数学计算和数据处理
- 使用安全的标准库（math, random, datetime, json, re等）
- 使用数据科学库（matplotlib, numpy, pandas等）
- 生成图表和可视化
- 安全地读取本地文件
- 提供三种执行模式：
  * strict: 最严格模式，禁用循环保护
  * normal: 标准模式，平衡安全性和功能性
  * permissive: 宽松模式，允许更多标准库和可视化

文件读取功能：
- 可以使用 safe_read_file(filepath) 函数安全地读取当前工作目录下的文件
- 支持文本文件和二进制文件（二进制文件会返回大小信息）
- 自动处理编码问题（UTF-8, GBK等）
- 有文件大小限制（默认10MB）
- 只能访问当前工作目录下的文件，禁止访问上级目录
- 示例用法：
  ```python
  # 读取文本文件
  content = safe_read_file("data.txt")

  # 读取Excel文件需要配合pandas
  import pandas as pd
  df = pd.read_excel("data.xlsx")  # 使用pandas读取Excel
  ```

安全限制：
- 禁止直接的文件I/O操作（open, file等），但可以使用safe_read_file
- 禁止网络访问
- 禁止导入危险模块（os, sys等）
- 有内存和执行时间限制
- 有循环迭代次数限制
- 文件访问仅限当前工作目录

重要提示：
- 当使用matplotlib、numpy、pandas等可视化或数据科学库时，必须使用permissive模式
- 当用户需要生成图表或进行数据可视化时，请设置mode为"permissive"
- 对于基本计算和标准库操作，使用normal或strict模式

MATPLOTLIB中文显示重要规则：
1. 生成图表时，系统已自动配置中文字体支持，无需手动设置字体
2. 直接使用中文文字作为标题、标签等，系统会自动处理字体
3. 不要添加任何字体设置代码，如plt.rcParams、font等
4. 不要使用matplotlib.use()或设置backend，系统已自动配置
5. 示例正确用法：
   ```python
   plt.title('中文标题')
   plt.xlabel('中文X轴标签') 
   plt.ylabel('中文Y轴标签')
   ```
6. 生成的图表会自动保存为PNG文件，用户可以查看

代码生成最佳实践：
- 使用permissive模式处理matplotlib代码
- 保持代码简洁，让系统自动处理字体和保存
- 在plt.close()前添加print语句说明图表已生成
- 使用合适的颜色和图表尺寸

当用户需要执行代码时，请使用execute_python_code函数。对于图表生成，系统会自动保存图片并提供查看方式。"""
        },
        {"role": "user", "content": user_message}
    ]
    
    print(f"\n💬 用户: {user_message}")
    
    # 最多进行5轮对话（防止无限循环）
    for round_num in range(5):
        print(f"\n🤖 LLM 响应 (轮次 {round_num + 1}):")
        
        try:
            response = client.chat.completions.create(
                model="Qwen/Qwen3-235B-A22B-Instruct-2507",
                messages=messages,
                tools=[PYTHON_EXECUTOR_FUNCTION],
                tool_choice="required",
                temperature=0.3,
                max_tokens=2000,  # 增加token限制以避免代码被截断
            )
            print(f"🤖 LLM 响应: {response}")
            message = response.choices[0].message
            
            # 添加助手回复到消息历史
            messages.append({
                "role": "assistant",
                "content": message.content,
                "tool_calls": message.tool_calls if message.tool_calls else None
            })
            
            # 如果有文本回复，显示它
            if message.content:
                print(f"💭 {message.content}")
            
            # 如果有function call，执行它们
            if message.tool_calls:
                for tool_call in message.tool_calls:
                    if tool_call.function.name == "execute_python_code":
                        try:
                            # 解析参数
                            args = json.loads(tool_call.function.arguments)

                            # 执行function call
                            result = execute_python_code_function(**args)
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析错误: {e}")
                            print(f"原始参数: {tool_call.function.arguments[:500]}...")
                            result = {
                                "success": False,
                                "error": f"LLM生成的代码参数解析失败: {str(e)}",
                                "executed": False
                            }
                        
                        # 添加function result到消息历史
                        messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": json.dumps(result, ensure_ascii=False)
                        })
                        
                        # 显示执行结果
                        print(f"\n📊 代码执行结果:")
                        print(f"   成功: {result['success']}")
                        if result['success'] and result['output']:
                            print(f"   输出: {result['output'].strip()}")
                        if result['error']:
                            print(f"   错误: {result['error']}")
                        if result.get('warnings'):
                            print(f"   警告: {', '.join(result['warnings'])}")
                        print(f"   执行时间: {result.get('execution_time', 0):.4f}秒")
                        
                        # 显示生成的文件信息
                        if result.get('generated_files'):
                            print(f"\n🖼️  生成的图片文件:")
                            for i, file_info in enumerate(result['generated_files'], 1):
                                print(f"   {i}. 文件: {file_info['filename']}")
                                print(f"      路径: {file_info['filepath']}")
                                print(f"      大小: {file_info['size']} bytes")
                                print(f"      格式: {file_info['format'].upper()}")
                                
                                # 保存base64到文件供查看
                                preview_path = f"/tmp/preview_{file_info['filename']}"
                                try:
                                    import base64
                                    with open(preview_path, 'wb') as f:
                                        f.write(base64.b64decode(file_info['base64']))
                                    print(f"      预览: {preview_path}")
                                except Exception as e:
                                    print(f"      预览保存失败: {e}")
            else:
                # 没有function call，对话结束
                break
                
        except Exception as e:
            print(f"❌ 对话过程中发生错误: {e}")
            break
    
    print("\n" + "="*50)


def main():
    """
    主执行函数 - Function Calling模式
    """
    global executor
    
    # --- LLM API 配置 ---
    api_key = os.getenv("OPENAI_API_KEY", "ms-b84bee6a-1833-499a-9616-0a09fccc9f4c")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api-inference.modelscope.cn/v1/")

    print("🚀 LLM Python代码执行助手 (Function Calling模式)")
    print(f"API Base URL: {base_url}")
    
    if not base_url:
        print("错误: 环境变量 'OPENAI_BASE_URL' 未设置。")
        sys.exit(1)

    try:
        client = OpenAI(api_key=api_key, base_url=base_url)
    except Exception as e:
        print(f"创建OpenAI客户端失败: {e}")
        sys.exit(1)

    # 初始化全局代码执行器
    config = {
        'timeout': 10,
        'max_loop_iterations': 100000,
        'max_memory_mb': 50,
        'max_output_length': 10000
    }
    executor = PythonCodeExecutor(config)
    
    # 显示工具能力信息
    capabilities = executor.get_capabilities()
    print(f"\n🛠️  工具: {capabilities['name']} v{capabilities['version']}")
    print(f"   支持模式: {', '.join(capabilities['supported_modes'])}")
    print(f"   安全特性: {len(capabilities['security_features'])} 项")
    print(f"   内存限制: {config['max_memory_mb']}MB")
    print(f"   超时限制: {config['timeout']}秒")

    # 测试用例
    test_cases = [
       # "请计算斐波那契数列的第20项",
       # "帮我计算半径为3的圆的面积和周长",
       # "生成10个1到100之间的随机数并计算它们的平均值",
       # "用Python解决这个问题：有一个列表[1,2,3,4,5]，请计算每个元素的平方",
       # "请演示一个试图读取文件的恶意代码",
       # "写一段代码来计算当前时间",
       # "计算从1加到1000的和",
       # "使用matplotlib生成一个简单的折线图，显示x=0到10，y=x²的函数图像（请使用permissive模式）",
       # "创建一个柱状图显示5个城市的人口数据：北京2154万，上海2424万，广州1532万，深圳1344万，杭州1036万（请使用permissive模式以支持中文显示）,并预览图片",
       # "生成一个散点图，显示20个随机点的分布（请使用permissive模式）,并预览图片",
       # "使用matplotlib画一个饼图显示编程语言使用率：Python 40%，JavaScript 25%，Java 20%，C++ 10%，其他 5%（请使用permissive模式）,并预览图片"
        "请读取当前目录下的1.xlsx文件，提取其中的身份证号码，并进行验证。需要：1）显示文件的基本信息（行数、列数、列名）；2）从所有列中搜索18位身份证号码；3）验证身份证号码的校验位是否正确；4）从有效的身份证号中提取出生日期和性别信息；5）统计结果并显示详细信息。请使用permissive模式以支持pandas读取Excel文件。"
    ]
    
    print(f"\n🧪 开始测试 {len(test_cases)} 个用例:")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}/{len(test_cases)}")
        chat_with_function_calls(client, test_case)
    
    print("\n🎉 所有测试完成！")
    print("\n💡 MCP工具封装要点:")
    print("   1. 使用PYTHON_EXECUTOR_FUNCTION作为工具schema")
    print("   2. 实现execute_python_code_function作为处理函数") 
    print("   3. 支持三种执行模式：strict, normal, permissive")
    print("   4. 完整的安全验证和错误处理")
    print("   5. 标准化的JSON响应格式")


if __name__ == "__main__":
    main()
