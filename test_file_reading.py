#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试沙箱文件读取功能和身份证号提取
"""

import re
from safe_sandbox import PythonCodeExecutor

def test_excel_id_extraction():
    """测试从Excel文件中提取身份证号"""
    
    print("=== 测试Excel文件读取和身份证号提取 ===")
    
    # 创建执行器
    executor = PythonCodeExecutor()
    
    # 测试代码：读取Excel文件并提取身份证号
    test_code = '''
import pandas as pd
import re

# 身份证号正则表达式
# 18位身份证：前17位数字 + 最后一位数字或X
id_pattern = r'\\b[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]\\b'

def extract_id_numbers(text):
    """从文本中提取身份证号"""
    if pd.isna(text):
        return []
    
    text = str(text)
    matches = re.findall(id_pattern, text)
    # 返回完整的身份证号
    full_matches = re.findall(r'\\b[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]\\b', text)
    return full_matches

def validate_id_number(id_num):
    """验证身份证号的校验位"""
    if len(id_num) != 18:
        return False
    
    # 权重系数
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    # 校验码对应表
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    try:
        # 计算前17位的加权和
        sum_val = sum(int(id_num[i]) * weights[i] for i in range(17))
        # 计算校验位
        check_index = sum_val % 11
        expected_check = check_codes[check_index]
        
        # 比较校验位（不区分大小写）
        return id_num[17].upper() == expected_check.upper()
    except (ValueError, IndexError):
        return False

try:
    # 读取Excel文件
    print("正在读取Excel文件: 1.xlsx")
    df = pd.read_excel('1.xlsx')
    
    print(f"文件读取成功！")
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print()
    
    # 显示前几行数据
    print("前5行数据:")
    print(df.head())
    print()
    
    # 在所有列中搜索身份证号
    all_id_numbers = []
    
    for col in df.columns:
        print(f"检查列: {col}")
        for idx, cell_value in enumerate(df[col]):
            if pd.notna(cell_value):
                ids = extract_id_numbers(str(cell_value))
                for id_num in ids:
                    is_valid = validate_id_number(id_num)
                    all_id_numbers.append({
                        'row': idx + 1,
                        'column': col,
                        'id_number': id_num,
                        'is_valid': is_valid,
                        'original_text': str(cell_value)[:50] + ('...' if len(str(cell_value)) > 50 else '')
                    })
                    print(f"  行{idx+1}: 发现身份证号 {id_num} ({'有效' if is_valid else '无效'})")
    
    print(f"\\n=== 提取结果汇总 ===")
    print(f"总共发现 {len(all_id_numbers)} 个身份证号")
    
    if all_id_numbers:
        valid_count = sum(1 for item in all_id_numbers if item['is_valid'])
        invalid_count = len(all_id_numbers) - valid_count
        
        print(f"有效身份证号: {valid_count} 个")
        print(f"无效身份证号: {invalid_count} 个")
        print()
        
        print("详细信息:")
        for i, item in enumerate(all_id_numbers, 1):
            print(f"{i}. 位置: 第{item['row']}行, {item['column']}列")
            print(f"   身份证号: {item['id_number']}")
            print(f"   状态: {'✓ 有效' if item['is_valid'] else '✗ 无效'}")
            print(f"   原始文本: {item['original_text']}")
            print()
    else:
        print("未发现任何身份证号")
        
        # 显示一些样本数据以便调试
        print("\\n样本数据（用于调试）:")
        for col in df.columns[:3]:  # 只显示前3列
            print(f"\\n列 '{col}' 的前5个值:")
            for i in range(min(5, len(df))):
                value = df[col].iloc[i]
                print(f"  [{i+1}] {repr(value)}")

except FileNotFoundError:
    print("错误: 找不到文件 '1.xlsx'")
    print("请确保文件存在于当前目录中")
except Exception as e:
    print(f"处理Excel文件时出错: {e}")
    import traceback
    traceback.print_exc()
'''
    
    # 执行测试代码
    result = executor.execute_code(test_code, mode="permissive")
    
    print("执行结果:")
    print(f"成功: {result['success']}")
    print(f"执行时间: {result['execution_time']:.2f}秒")
    
    if result['success']:
        print("输出:")
        print(result['output'])
    else:
        print("错误:")
        print(result['error'])
    
    if result['warnings']:
        print("警告:")
        for warning in result['warnings']:
            print(f"  - {warning}")

def test_simple_file_reading():
    """测试简单的文件读取功能"""
    
    print("\n=== 测试安全文件读取功能 ===")
    
    executor = PythonCodeExecutor()
    
    # 测试代码：使用内置的safe_read_file函数
    test_code = '''
# 测试安全文件读取
try:
    # 尝试读取Excel文件（二进制文件）
    content = safe_read_file("1.xlsx")
    print(f"文件读取成功，内容类型: {type(content)}")
    print(f"内容预览: {content[:100]}...")
    
except Exception as e:
    print(f"读取失败: {e}")

# 测试读取不存在的文件
try:
    content = safe_read_file("nonexistent.txt")
except Exception as e:
    print(f"预期的错误（文件不存在）: {e}")

# 测试读取工作目录外的文件（安全测试）
try:
    content = safe_read_file("../../../etc/passwd")
except Exception as e:
    print(f"预期的安全错误: {e}")
'''
    
    result = executor.execute_code(test_code, mode="normal")
    
    print("执行结果:")
    print(f"成功: {result['success']}")
    
    if result['success']:
        print("输出:")
        print(result['output'])
    else:
        print("错误:")
        print(result['error'])

if __name__ == "__main__":
    # 运行测试
    test_simple_file_reading()
    test_excel_id_extraction()
