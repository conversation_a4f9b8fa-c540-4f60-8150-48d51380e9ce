#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试文件读取功能
"""

from safe_sandbox import PythonCodeExecutor

def test_basic_file_reading():
    """测试基本文件读取功能"""
    
    print("=== 测试基本文件读取功能 ===")
    
    executor = PythonCodeExecutor()
    
    # 测试代码：使用内置的safe_read_file函数
    test_code = '''
print("测试safe_read_file函数...")

# 测试读取Excel文件（二进制文件）
try:
    content = safe_read_file("1.xlsx")
    print(f"✓ 文件读取成功")
    print(f"内容类型: {type(content)}")
    print(f"内容长度: {len(content)} 字符")
    if content.startswith("[二进制文件"):
        print("✓ 正确识别为二进制文件")
    else:
        print(f"内容预览: {content[:100]}...")
        
except Exception as e:
    print(f"✗ 读取失败: {e}")

print("\\n" + "="*50)

# 测试安全限制
print("测试安全限制...")

# 测试读取不存在的文件
try:
    content = safe_read_file("nonexistent.txt")
    print("✗ 应该失败但没有失败")
except Exception as e:
    print(f"✓ 正确拒绝不存在的文件: {type(e).__name__}")

# 测试读取工作目录外的文件（安全测试）
try:
    content = safe_read_file("../../../etc/passwd")
    print("✗ 安全检查失败！")
except Exception as e:
    print(f"✓ 正确拒绝目录外访问: {type(e).__name__}")

print("\\n测试完成！")
'''
    
    result = executor.execute_code(test_code, mode="normal")
    
    print("执行结果:")
    print(f"成功: {result['success']}")
    print(f"执行时间: {result['execution_time']:.2f}秒")
    
    if result['success']:
        print("\\n输出:")
        print(result['output'])
    else:
        print("\\n错误:")
        print(result['error'])
    
    if result['warnings']:
        print("\\n警告:")
        for warning in result['warnings']:
            print(f"  - {warning}")

def test_pandas_excel_reading():
    """测试使用pandas读取Excel文件"""
    
    print("\\n=== 测试pandas读取Excel文件 ===")
    
    executor = PythonCodeExecutor()
    
    # 增加超时时间
    executor.sandbox.timeout = 15
    
    test_code = '''
import pandas as pd

print("使用pandas读取Excel文件...")

try:
    # 读取Excel文件
    df = pd.read_excel('1.xlsx')
    
    print(f"✓ Excel文件读取成功！")
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 显示前几行数据
    print("\\n前3行数据:")
    print(df.head(3))
    
    # 检查数据类型
    print("\\n数据类型:")
    print(df.dtypes)
    
except Exception as e:
    print(f"✗ 读取失败: {e}")
    import traceback
    traceback.print_exc()
'''
    
    result = executor.execute_code(test_code, mode="permissive")
    
    print("执行结果:")
    print(f"成功: {result['success']}")
    print(f"执行时间: {result['execution_time']:.2f}秒")
    
    if result['success']:
        print("\\n输出:")
        print(result['output'])
    else:
        print("\\n错误:")
        print(result['error'])

if __name__ == "__main__":
    test_basic_file_reading()
    test_pandas_excel_reading()
